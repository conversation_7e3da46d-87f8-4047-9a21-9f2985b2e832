import { generateObject } from 'ai'
import { templateAssessmentResponseSchema } from '@/app/libs/api-schemas'
import {
  getModelConfig,
  getModelFromConfig,
  getProviderNameForUsage,
} from '@/app/libs/model-config'
import { createAIUsage } from '@/app/server-actions/log_ai_usage'
import { AIUsageType } from '@prisma/client'

export type TemplateAssessmentResponse = {
  suitable: boolean
  reasons: string[]
  customTemplate?: string
}

/**
 * Assess whether the default decision-analyst template is suitable for the task.
 * Uses separate low-cost model config: dragtree_research_generate_flow
 */
export async function assessTemplate(
  userId: string,
  contentId: string, // kept for logging alignment and future auditing
  originalAsk: string,
  researchTask: string
): Promise<TemplateAssessmentResponse | null> {
  try {
    const templateExample = `
# Answer
  1–2 punchy sentences that directly resolve the RESEARCH_TASK in the context of the ORIGINAL_ASK. Include 1–2 impact numbers or ranges (illustrative is fine). Every number here must appear in **Assumptions** and **Tiny math**.

## Why this happens
  3–6 bullets capturing core drivers (motivations, constraints, context, trade-offs). One clear idea + one plain explanation per bullet. Add minimal, credible citations when available.

## Assumptions & sources (quick scan)
  Present 4–8 key variables (“knobs”) in a table. If a high-volatility item isn’t verified, mark **Assumed** and you’ll flag it under *Confidence & freshness*.

  | Knob / Variable | Value / Range | Scope (Local/Global/Analog) | Provenance (Source <title, YYYY-MM> or **Assumed** + 2–5-word reason) | Volatility (High/Med/Low) | Notes |
  | --------------- | ------------: | --------------------------- | ---------------------------------------------------------------------- | ------------------------- | ----- |

## Tiny math
  Show 1–3 explicit lines that connect assumptions to the claim in **Answer**. Pick any pattern that fits the domain (examples, not prescriptions):
   * scale × rate × time
   * capacity × utilization
   * probability × impact (expected value)
   * size × cost per unit
   * performance = baseline ± delta
   * sample size / power (if research)
     Format each line as **Formula = Numbers = Result**. Ceil any breakeven thresholds/headcounts.
     **Optional — Mermaid (if it helps):** a tiny flowchart of the math or decision gate (≤8 nodes).

## What could flip the answer
   1–3 concrete flip conditions (e.g., “if realized price < Z”, “if throughput < X”, “if regulation Y passes”, “if effect size < E”). Make them specific and testable.

## Next step / gate
   One practical next action and a simple go/no-go gate appropriate to the domain (e.g., “Proceed if presales ≥ N”, “Advance if P95 latency < T ms”, “Continue if pilot effect size ≥ δ”, “Move forward if safety margin ≥ S”). One line each.

## Confidence & freshness
   One compact paragraph that gives *signals*, not formalities:
   * **Confidence label** (High / Med-High / Med / Low) **+ why** (source quality, corroboration, locality/transferability, quantified reasoning, assumption burden).
   * **Freshness by volatility** (what changes fast vs. slow).
   * **Blind spots that matter** (1–2 bullets).
   * **Re-verify now** list (1–3 high-vol items to check next).

## Sources
   List **3–5 decisive items** actually used. Prefer primary/official docs.
   Format: **Title — Publisher (YYYY-MM)** with a clean link. No meta chatter.

**Behavior**

* Every number in **# Answer** must trace to **Assumptions** and appear in **Tiny math**.
* Don’t invent precise fresh facts; use sensible ranges and mark **Assumed** where needed; surface key uncertainties in *Confidence & freshness*.
* If tools/browsing are available, do **targeted** lookups for high-volatility or missing items; cite those. Avoid filler sources.
* If credible sources conflict, note it briefly and choose a conservative assumption for the math.
* Keep it domain-agnostic: select variables and math patterns that fit the problem (science, policy, SaaS, hardware, health, etc.); don’t force business-specific jargon.
`

    const systemPrompt = `You are a decision analyst and research methodology expert. Your task is to assess whether a given research template is suitable for a specific research question, and if not, generate a customized research flow.

**Assessment Criteria:**
- Template scope and focus alignment with research question
- Methodology appropriateness for the domain
- Required analysis depth and complexity
- Specialized knowledge or frameworks needed

**Template Example (Decision Analyst Template):**
{
${templateExample}
}

**Instructions:**
1. Analyze the research question against the template example
2. Determine if the template is suitable (true/false)
3. Provide clear reasons for your assessment in short phrase, don't be long, 10-20 words max
4. If unsuitable, generate a custom research flow [Only the template like ] in markdown format

**Custom Flow (if needed):**
- Must include "Answer" section
- Must include "Assumptions & sources" section
- Must include "Confidence & freshness" section
- Use tables when comparing multiple options with appropriate column suggestions
- Include "Tiny Math" micro-blocks when quantitative analysis is relevant
- Structure should be domain-appropriate and comprehensive
- Use clear markdown formatting with proper headers and sections
- You just need to provide direction similar to our template, next model will handle the details
`

    const userPrompt = `**Original User Ask:** "${originalAsk}"

**Research Task:** "${researchTask}"

Please assess whether the decision analyst template is suitable for this research question and provide your structured response.`

    // Use flow config for two-step flow if available, otherwise default
    // Note: We keep the original route key for backward compatibility
    const modelConfig = await getModelConfig(
      userId,
      'dragtree_2steps_research_generate_flow'
    ).catch(() =>
      getModelConfig(userId, 'dragtree_2steps_research_generate_flow')
    )

    const model = getModelFromConfig(modelConfig)

    // Execute with providerOptions when present
    const result = await generateObject({
      model,
      schema: templateAssessmentResponseSchema,
      system: systemPrompt,
      prompt: userPrompt,
      temperature: modelConfig.temperature,
      maxOutputTokens: modelConfig.maxOutputTokens,
      providerOptions: modelConfig.providerOptions as any,
    })

    // Non-blocking usage logging for flow assessment stage
    try {
      await createAIUsage({
        userId,
        entityType: 'drag_tree_node_content',
        entityId: contentId,
        aiProvider: getProviderNameForUsage(modelConfig),
        modelName: modelConfig.model,
        usageType: AIUsageType.NODE_QUICK_RESEARCH,
        inputPrompt: systemPrompt,
        messages: [{ role: 'user', content: userPrompt }],
        metadata: {
          stage: 'two-step-flow-assessment',
          tokenUsage: result.usage,
          model: modelConfig.model,
          providerOptions: modelConfig.providerOptions || {},
          responseType: 'object',
        },
        config: {
          temperature: modelConfig.temperature,
          maxOutputTokens: modelConfig.maxOutputTokens,
        },
      })
    } catch (err) {
      console.error('📊 AI usage logging failed for flow assessment:', err)
    }

    return result.object
  } catch (error) {
    console.error('Error in template assessment:', error)
    return null
  }
}
