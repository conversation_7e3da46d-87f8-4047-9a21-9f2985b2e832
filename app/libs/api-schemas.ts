import { z } from 'zod'
import { DragTreeStatus } from '@prisma/client'
import { isValidLanguageCode } from '@/app/constants/languages'

// ==================== DRAG TREE SCHEMAS ====================

// Initialize drag tree request
export const initializeDragTreeSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255, 'Title too long'),
  description: z.string().min(1, 'Description is required'),
  preferredLanguage: z
    .string()
    .length(2, 'Language code must be exactly 2 characters')
    .refine(isValidLanguageCode, 'Invalid language code')
    .optional()
    .default('en'),
  // Support both legacy array format and new structured format
  analysisData: z
    .union([
      z.array(z.string()), // Legacy format
      z.object({
        is_problem_clear: z.boolean(),
        problem_ambiguity: z.string(),
        intention: z.array(z.string()),
        entity: z.array(z.string()),
        score: z.number(),
        pass: z.boolean(),
      }),
    ])
    .optional(),
})

// Load drag tree query params
export const loadDragTreeSchema = z.object({
  id: z.string().min(1, 'Drag tree ID is required'),
})

// Generate questions request
export const generateQuestionsSchema = z.object({
  dragTreeId: z.string().min(1, 'Drag tree ID is required'),
  regenerate: z.boolean().optional().default(false),
})

// Create drag tree node content
export const createNodeContentSchema = z.object({
  nodeId: z.string().min(1, 'Node ID is required'),
  content: z.string().min(1, 'Content is required'),
  type: z
    .enum(['RESEARCH', 'QUESTION', 'ANSWER'])
    .optional()
    .default('RESEARCH'),
})

// Update drag tree node content
export const updateNodeContentSchema = z.object({
  nodeId: z.string().min(1, 'Node ID is required'),
  content: z.string().min(1, 'Content is required'),
})

// Research generation request - unified schema for both chat and direct formats
export const researchGenerateSchema = z.union([
  // Direct API format
  z.object({
    contentId: z.string().min(1, 'Content ID is required'),
    questionText: z.string().min(1, 'Question text is required'),
    researchType: z.string().optional(),
  }),
  // Chat format (from useChat hook)
  z.object({
    messages: z
      .array(
        z.object({
          role: z.enum(['user', 'assistant', 'system']),
          content: z.string(),
        })
      )
      .min(1, 'At least one message is required'),
    // Optional direct parameters that can be included alongside messages
    contentId: z.string().optional(),
    questionText: z.string().optional(),
  }),
])

// Update drag tree title
export const updateDragTreeTitleSchema = z.object({
  dragTreeId: z.string().min(1, 'Drag tree ID is required'),
  title: z.string().min(1, 'Title is required').max(255, 'Title too long'),
})

// Batch create nodes
export const batchCreateNodesSchema = z.object({
  dragTreeId: z.string().min(1, 'Drag tree ID is required'),
  nodes: z
    .array(
      z.object({
        title: z.string().min(1, 'Node title is required'),
        type: z
          .enum(['RESEARCH', 'QUESTION', 'ANSWER'])
          .optional()
          .default('QUESTION'),
        parentId: z.string().nullable().optional(),
        order: z.number().int().min(0).optional(),
      })
    )
    .min(1, 'At least one node is required'),
})

// ==================== SCREENING SCHEMAS ====================

// Common screening request schema (already well-defined in your codebase)
export const screeningRequestSchema = z.object({
  userId: z.string().min(1, 'userId is required'),
  description: z.string().min(1, 'description is required'),
  preferredLanguage: z.string().optional().default('en'),
})

// Screen analysis response schema (from your existing utils)
export const screenAnalysisSchema = z.object({
  is_problem_clear: z.boolean(),
  problem_ambiguity: z.string(),
  intention: z.array(z.string()),
  entity: z.array(z.string()),
  score: z.number(),
  pass: z.boolean(),
})

// Rephrase response schema
export const rephraseResponseSchema = z.array(z.string()).length(4)

// ==================== COMMON PARAMETER SCHEMAS ====================

// User ID validation
export const userIdParamSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
})

// Drag tree ID validation
export const dragTreeIdParamSchema = z.object({
  dragTreeId: z.string().min(1, 'Drag tree ID is required'),
})

// Node ID validation
export const nodeIdParamSchema = z.object({
  nodeId: z.string().min(1, 'Node ID is required'),
})

// Pagination schema
export const paginationSchema = z.object({
  page: z.coerce.number().int().min(1).optional().default(1),
  limit: z.coerce.number().int().min(1).max(100).optional().default(10),
})

// Language preference schema - enforces two-letter language codes only
export const languageSchema = z.object({
  preferredLanguage: z
    .string()
    .length(2, 'Language code must be exactly 2 characters')
    .refine(isValidLanguageCode, 'Invalid language code')
    .optional()
    .default('en'),
})

// ==================== STATUS VALIDATION SCHEMAS ====================

// Drag tree status validation
export const dragTreeStatusSchema = z.object({
  status: z.nativeEnum(DragTreeStatus),
})

// Status update schema
export const updateStatusSchema = z.object({
  dragTreeId: z.string().min(1, 'Drag tree ID is required'),
  status: z.nativeEnum(DragTreeStatus),
})

// ==================== COMPOSITE SCHEMAS ====================

// For endpoints that need multiple validations
export const dragTreeWithStatusSchema = dragTreeIdParamSchema.extend({
  status: z.nativeEnum(DragTreeStatus).optional(),
})

// For endpoints with pagination and filtering
export const listDragTreesSchema = paginationSchema.extend({
  status: z.nativeEnum(DragTreeStatus).optional(),
  search: z.string().optional(),
})

// For AI generation endpoints
export const aiGenerationBaseSchema = z.object({
  model: z.string().optional().default('gpt-4o-mini'),
  temperature: z.number().min(0).max(2).optional().default(0.7),
  maxOutputTokens: z.number().int().min(1).max(4000).optional().default(2000),
})

// Template assessment request schema for research flow generation
export const templateAssessmentRequestSchema = z.object({
  originalAsk: z.string().min(1, 'Original ask is required'),
  researchTask: z.string().min(1, 'Research task is required'),
  templateExample: z.string().min(1, 'Template example is required'),
})

// Template assessment response schema for structured output
export const templateAssessmentResponseSchema = z.object({
  suitable: z
    .boolean()
    .describe(
      'Whether the current template is suitable for this research question'
    ),
  reasons: z
    .array(z.string())
    .describe(
      'Array of SHORT [<20 words each] reasons explaining the suitability assessment'
    ),
  customTemplate: z
    .string()
    .optional()
    .describe(
      'Custom research template [Focus on TEMPLATE that fits the question, details will be filled by another model] in markdown format if suitable=false. Stop immediately after the final requested section, Do NOT append any follow-up offers, next-step suggestions unless explicitly requested.'
    ),
})

// ==================== TYPE EXPORTS ====================

// Export inferred types for use in server actions and API routes
export type InitializeDragTreeInput = z.infer<typeof initializeDragTreeSchema>
export type LoadDragTreeInput = z.infer<typeof loadDragTreeSchema>
export type GenerateQuestionsInput = z.infer<typeof generateQuestionsSchema>
export type CreateNodeContentInput = z.infer<typeof createNodeContentSchema>
export type UpdateNodeContentInput = z.infer<typeof updateNodeContentSchema>
export type ResearchGenerateInput = z.infer<typeof researchGenerateSchema>
export type UpdateDragTreeTitleInput = z.infer<typeof updateDragTreeTitleSchema>
export type BatchCreateNodesInput = z.infer<typeof batchCreateNodesSchema>
export type ScreeningRequestInput = z.infer<typeof screeningRequestSchema>
export type ScreenAnalysisOutput = z.infer<typeof screenAnalysisSchema>
export type RephraseResponseOutput = z.infer<typeof rephraseResponseSchema>
export type ListDragTreesInput = z.infer<typeof listDragTreesSchema>
export type AIGenerationBaseInput = z.infer<typeof aiGenerationBaseSchema>
